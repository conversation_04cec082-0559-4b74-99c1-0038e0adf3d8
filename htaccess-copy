<IfModule mod_rewrite.c>
    RewriteEngine On
    #RewriteBase /amis_six/

    # Redirect requests for /public/anything to /anything
    RewriteCond %{THE_REQUEST} /public/([^\s?]*) [NC]
    RewriteRule ^ %1 [L,NE,R=302]

    # Rewrite for URLs that don't exist in the root to the public folder
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php/$1 [L,QSA]
</IfModule>