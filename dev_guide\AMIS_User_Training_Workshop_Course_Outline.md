# AMIS System User Tutorial Workshop - One Week Course Outline

## Course Overview

**Course Title:** Agricultural Management Information System (AMIS Five) - User Training Workshop  
**Duration:** 5 Days (40 Hours)  
**Target Audience:** Admins, Supervisors, M&E Evaluators, Regular Users  
**Delivery Mode:** Hands-on practical training with live system access  
**Prerequisites:** Basic computer literacy and understanding of agricultural planning processes  

---

## **DAY 1: SYSTEM FOUNDATION & USER MANAGEMENT**

### **Morning Session (9:00 AM - 12:00 PM)**

#### **Session 1.1: AMIS System Introduction (9:00 - 10:30 AM)**
- **Learning Objectives:**
  - Understand AMIS Five purpose and scope
  - Identify key system modules and their relationships
  - Recognize the role of AMIS in PNG's agricultural development

- **Topics Covered:**
  - What is AMIS Five - Agricultural Management Information System
  - System architecture and technology stack (CodeIgniter 4)
  - Integration with PNG government structure (Province → District → LLG → Ward)
  - Strategic planning framework integration (NASP, MTDP, Corporate Plans)
  - User roles and access levels overview

- **Practical Exercise:**
  - System tour and navigation walkthrough
  - Accessing AMIS via XAMPP environment
  - Understanding the dashboard layout

#### **Break (10:30 - 10:45 AM)**

#### **Session 1.2: Authentication & Security (10:45 AM - 12:00 PM)**
- **Learning Objectives:**
  - Master login procedures and security protocols
  - Understand role-based access control (RBAC)
  - Learn password management and account security

- **Topics Covered:**
  - Login process and session management
  - Role-based permissions (Admin, Supervisor, User, M&E, Commodity)
  - Password policies and security best practices
  - Multi-system integration (AMIS + Dakoii)
  - Account activation and password reset procedures

- **Practical Exercise:**
  - Login practice with different user roles
  - Profile management and password changes
  - Security feature demonstration

### **Afternoon Session (1:00 PM - 5:00 PM)**

#### **Session 1.3: User Management (Admin Focus) (1:00 - 3:00 PM)**
- **Learning Objectives:**
  - Create and manage user accounts
  - Assign roles and permissions
  - Understand organizational hierarchy setup

- **Topics Covered:**
  - User creation and management workflows
  - Role assignment and capability management
  - Branch and government structure setup
  - Supervisor assignment and reporting relationships
  - User status management and activation

- **Practical Exercise:**
  - Create sample user accounts for different roles
  - Set up organizational hierarchy
  - Practice user activation and deactivation

#### **Break (3:00 - 3:15 PM)**

#### **Session 1.4: System Administration Basics (3:15 - 5:00 PM)**
- **Learning Objectives:**
  - Configure basic system settings
  - Understand administrative controls
  - Learn system monitoring basics

- **Topics Covered:**
  - Organization settings configuration
  - Regional and branch management
  - Government structure administration
  - System logs and monitoring
  - Email notification setup

- **Practical Exercise:**
  - Configure organization settings
  - Set up regional structure
  - Test email notifications

---

## **DAY 2: STRATEGIC PLANNING & WORKPLAN MANAGEMENT**

### **Morning Session (9:00 AM - 12:00 PM)**

#### **Session 2.1: Strategic Planning Framework (9:00 - 10:30 AM)**
- **Learning Objectives:**
  - Understand PNG's strategic planning hierarchy
  - Learn to navigate and manage strategic plans
  - Connect activities to strategic objectives

- **Topics Covered:**
  - NASP (National Agriculture Sector Plan) structure and management
  - MTDP (Medium Term Development Plan) hierarchy
  - Corporate Plans integration
  - Strategic plan linkages and relationships
  - Plan status tracking and updates

- **Practical Exercise:**
  - Navigate NASP plan structure (Plans → APAs → DIPs → Specific Areas → Objectives → Outputs → Indicators)
  - Explore MTDP hierarchy (Plans → SPAs → DIPs → Specific Areas → Investments → KRAs → Strategies → Indicators)
  - Review Corporate Plans structure

#### **Break (10:30 - 10:45 AM)**

#### **Session 2.2: Workplan Creation & Management (10:45 AM - 12:00 PM)**
- **Learning Objectives:**
  - Create comprehensive workplans
  - Assign supervisors and manage workplan lifecycle
  - Understand workplan status tracking

- **Topics Covered:**
  - Workplan creation workflow
  - Supervisor assignment and notification system
  - Workplan objectives and description management
  - Date planning and timeline management
  - Status tracking (draft, in_progress, completed, on_hold)

- **Practical Exercise:**
  - Create a sample workplan
  - Assign supervisors and set timelines
  - Practice workplan editing and status updates

### **Afternoon Session (1:00 PM - 5:00 PM)**

#### **Session 2.3: Activity Management - Core Concepts (1:00 - 2:30 PM)**
- **Learning Objectives:**
  - Understand the four activity types
  - Learn activity creation and management
  - Master GPS coordination and location tracking

- **Topics Covered:**
  - Four activity types: Training, Inputs, Infrastructure, Output
  - Activity creation workflow and requirements
  - GPS coordinates and location management
  - Quarterly target setting and tracking
  - Budget allocation and cost management

- **Practical Exercise:**
  - Create activities for each type
  - Set GPS coordinates and locations
  - Configure quarterly targets

#### **Break (2:30 - 2:45 PM)**

#### **Session 2.4: Activity Types Deep Dive (2:45 - 5:00 PM)**
- **Learning Objectives:**
  - Master specific requirements for each activity type
  - Understand documentation and evidence requirements
  - Learn activity linking to strategic plans

- **Topics Covered:**
  - **Training Activities:** Trainer management, topic planning, trainee tracking
  - **Input Activities:** Supply distribution, beneficiary management, delivery tracking
  - **Infrastructure Activities:** Construction planning, progress monitoring, completion verification
  - **Output Activities:** Production tracking, quality control, market linkages

- **Practical Exercise:**
  - Create detailed activities for each type
  - Link activities to strategic plan components
  - Practice documentation upload

---

## **DAY 3: PROPOSAL WORKFLOW & APPROVAL PROCESSES**

### **Morning Session (9:00 AM - 12:00 PM)**

#### **Session 3.1: Proposal System Overview (9:00 - 10:30 AM)**
- **Learning Objectives:**
  - Understand the proposal workflow
  - Learn approval hierarchy and processes
  - Master proposal status tracking

- **Topics Covered:**
  - Proposal creation from activities
  - Supervisor assignment and notification workflow
  - Action officer assignment process
  - Proposal status lifecycle (pending → submitted → approved → rated)
  - Email notification system

- **Practical Exercise:**
  - Create proposals from existing activities
  - Navigate proposal dashboard
  - Review notification settings

#### **Break (10:30 - 10:45 AM)**

#### **Session 3.2: Supervisor Functions (10:45 AM - 12:00 PM)**
- **Learning Objectives:**
  - Master supervisor approval workflows
  - Learn action officer assignment
  - Understand supervision and verification processes

- **Topics Covered:**
  - Proposal review and approval process
  - Action officer selection and assignment
  - Implementation supervision workflow
  - Verification and quality control procedures
  - Feedback and revision management

- **Practical Exercise:**
  - Practice proposal approval workflow
  - Assign action officers to proposals
  - Conduct supervision verification

### **Afternoon Session (1:00 PM - 5:00 PM)**

#### **Session 3.3: Implementation & Documentation (1:00 - 3:00 PM)**
- **Learning Objectives:**
  - Learn implementation documentation requirements
  - Master file upload and evidence management
  - Understand signing sheet and attendance tracking

- **Topics Covered:**
  - Implementation evidence requirements by activity type
  - File upload procedures and security
  - Signing sheet management and GPS verification
  - Photo documentation and image management
  - Progress reporting and status updates

- **Practical Exercise:**
  - Upload implementation evidence
  - Create and manage signing sheets
  - Practice photo documentation

#### **Break (3:00 - 3:15 PM)**

#### **Session 3.4: Quality Control & Verification (3:15 - 5:00 PM)**
- **Learning Objectives:**
  - Understand verification procedures
  - Learn quality control standards
  - Master submission and approval workflows

- **Topics Covered:**
  - Implementation verification standards
  - Quality control checklists
  - Supervisor verification process
  - Submission for M&E evaluation
  - Feedback and improvement cycles

- **Practical Exercise:**
  - Conduct verification procedures
  - Submit activities for evaluation
  - Practice quality control processes

---

## **DAY 4: MONITORING & EVALUATION (M&E) SYSTEM**

### **Morning Session (9:00 AM - 12:00 PM)**

#### **Session 4.1: M&E System Overview (9:00 - 10:30 AM)**
- **Learning Objectives:**
  - Understand M&E role in AMIS
  - Learn evaluation access and permissions
  - Master M&E dashboard navigation

- **Topics Covered:**
  - M&E evaluator role and capabilities
  - Access control (admin OR is_evaluator = 1)
  - Evaluation dashboard and activity filtering
  - Performance metrics and rating systems
  - Quarterly achievement tracking

- **Practical Exercise:**
  - Access M&E evaluation module
  - Navigate approved activities for evaluation
  - Review evaluation dashboard

#### **Break (10:30 - 10:45 AM)**

#### **Session 4.2: Activity Evaluation Process (10:45 AM - 12:00 PM)**
- **Learning Objectives:**
  - Master activity evaluation procedures
  - Learn rating systems and criteria
  - Understand quarterly achievement assessment

- **Topics Covered:**
  - Activity evaluation workflow
  - Rating scale (1-5) and criteria
  - Quarterly achievement vs. target analysis
  - Implementation quality assessment
  - Evaluation remarks and feedback

- **Practical Exercise:**
  - Evaluate training activities
  - Rate infrastructure implementations
  - Assess input distribution activities

### **Afternoon Session (1:00 PM - 5:00 PM)**

#### **Session 4.3: Performance Metrics & Analytics (1:00 - 3:00 PM)**
- **Learning Objectives:**
  - Understand performance measurement
  - Learn analytics and trend analysis
  - Master comparative evaluation

- **Topics Covered:**
  - Performance indicator tracking
  - Trend analysis and pattern recognition
  - Comparative performance assessment
  - Success rate calculations
  - Impact measurement methodologies

- **Practical Exercise:**
  - Analyze performance trends
  - Generate performance comparisons
  - Calculate success rates

#### **Break (3:00 - 3:15 PM)**

#### **Session 4.4: M&E Reporting & Recommendations (3:15 - 5:00 PM)**
- **Learning Objectives:**
  - Create evaluation reports
  - Develop improvement recommendations
  - Learn feedback delivery systems

- **Topics Covered:**
  - Evaluation report generation
  - Recommendation development
  - Feedback delivery to implementers
  - Performance improvement planning
  - M&E data integration with reporting

- **Practical Exercise:**
  - Generate evaluation reports
  - Develop improvement recommendations
  - Practice feedback delivery

---

## **DAY 5: REPORTING, ANALYTICS & SYSTEM OPTIMIZATION**

### **Morning Session (9:00 AM - 12:00 PM)**

#### **Session 5.1: Comprehensive Reporting System (9:00 - 10:30 AM)**
- **Learning Objectives:**
  - Master all reporting modules
  - Understand data visualization features
  - Learn export and sharing capabilities

- **Topics Covered:**
  - **MTDP Reports:** Investment analysis, status distribution, entity tracking
  - **NASP Reports:** Sector performance, objective tracking, output analysis
  - **Workplan Reports:** Activity progress, budget utilization, timeline analysis
  - **Activity Maps:** Geographic visualization, GPS tracking, location analysis
  - **Commodity Reports:** Market analysis, price tracking, production data

- **Practical Exercise:**
  - Generate reports from each module
  - Practice data filtering and customization
  - Export reports in multiple formats

#### **Break (10:30 - 10:45 AM)**

#### **Session 5.2: Data Visualization & Analytics (10:45 AM - 12:00 PM)**
- **Learning Objectives:**
  - Master chart generation and interpretation
  - Learn interactive dashboard features
  - Understand data drill-down capabilities

- **Topics Covered:**
  - Interactive chart types (bar, pie, stacked, line)
  - Dashboard customization and filtering
  - Data drill-down and detailed analysis
  - Geographic mapping and GPS visualization
  - Trend analysis and forecasting

- **Practical Exercise:**
  - Create custom charts and visualizations
  - Practice interactive dashboard features
  - Analyze geographic activity distribution

### **Afternoon Session (1:00 PM - 5:00 PM)**

#### **Session 5.3: Advanced Features & Integration (1:00 - 2:30 PM)**
- **Learning Objectives:**
  - Explore advanced system features
  - Understand integration capabilities
  - Learn optimization techniques

- **Topics Covered:**
  - Document management and file organization
  - Meeting management and scheduling
  - Email notification customization
  - System integration with external services
  - Mobile responsiveness and accessibility

- **Practical Exercise:**
  - Organize document libraries
  - Schedule and manage meetings
  - Customize notification preferences

#### **Break (2:30 - 2:45 PM)**

#### **Session 5.4: Troubleshooting & Best Practices (2:45 - 4:00 PM)**
- **Learning Objectives:**
  - Learn common troubleshooting procedures
  - Understand system maintenance
  - Master best practice implementation

- **Topics Covered:**
  - Common user issues and solutions
  - Data backup and recovery procedures
  - Performance optimization tips
  - Security best practices
  - User support and help resources

- **Practical Exercise:**
  - Practice troubleshooting scenarios
  - Implement security best practices
  - Optimize system performance

#### **Session 5.5: Assessment & Certification (4:00 - 5:00 PM)**
- **Learning Objectives:**
  - Demonstrate system competency
  - Receive certification
  - Plan ongoing learning

- **Activities:**
  - Practical assessment covering all modules
  - Q&A session and clarifications
  - Certificate presentation
  - Resource sharing and ongoing support planning

---

## **Training Materials & Resources**

### **Required Materials:**
- AMIS Five system access (XAMPP environment)
- User manuals and quick reference guides
- Sample data sets for practice
- Assessment rubrics and checklists

### **Technical Requirements:**
- Computers with web browsers
- XAMPP server setup
- Internet connectivity for email testing
- Projector for demonstrations

### **Support Resources:**
- System administrator contact information
- Online help documentation
- Video tutorials for complex procedures
- User community forum access

---

## **Assessment Criteria**

### **Daily Assessments:**
- Practical exercises completion (70%)
- Participation and engagement (20%)
- Knowledge checks and quizzes (10%)

### **Final Certification Requirements:**
- Complete all daily sessions (mandatory)
- Pass practical assessment (minimum 80%)
- Demonstrate competency in role-specific functions
- Submit completed practice scenarios

---

## **Post-Training Support**

### **Ongoing Support Plan:**
- 30-day follow-up sessions
- Monthly user group meetings
- Quarterly system updates training
- Help desk support contact information
- Advanced user training opportunities

---

## **Course Summary**

This comprehensive course outline ensures all user types receive appropriate training for their roles while building a strong foundation in AMIS system usage and agricultural management processes.

### **Key Features of the Course:**

**🎯 Structured Learning Path:**
- Day 1: Foundation & User Management
- Day 2: Strategic Planning & Workplans
- Day 3: Proposal Workflows & Approvals
- Day 4: Monitoring & Evaluation (M&E)
- Day 5: Reporting & System Optimization

**👥 Role-Based Training:**
- Admin-focused sessions for user management and system administration
- Supervisor training for approval workflows and oversight
- M&E specific modules for evaluation and performance tracking
- Universal training for all users on core functionality

**🛠️ Practical Approach:**
- Hands-on exercises with live XAMPP environment
- Real-world scenarios using actual AMIS features
- Progressive skill building from basic to advanced

**📊 Comprehensive Coverage:**
- All major AMIS modules (Workplans, Activities, Proposals, Reports)
- Strategic planning integration (NASP, MTDP, Corporate Plans)
- Complete workflow from planning to evaluation
- Advanced features like mapping, analytics, and document management

**🎓 Assessment & Certification:**
- Daily practical assessments
- Final competency demonstration
- Certification upon successful completion
- Post-training support plan

The course is designed for 40 hours of intensive training with a mix of theoretical understanding and practical application, ensuring participants can effectively use the AMIS system in their respective roles within PNG's Department of Agriculture and Livestock.
