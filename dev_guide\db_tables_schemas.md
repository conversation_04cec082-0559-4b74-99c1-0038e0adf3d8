Showing create queries
Tables
Table	Create table
activities	CREATE TABLE `activities` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `workplan_period_id` int(11) NOT NULL,
 `performance_output_id` int(11) NOT NULL,
 `supervisor_id` int(11) DEFAULT NULL,
 `action_officer_id` int(11) DEFAULT NULL,
 `activity_title` varchar(500) NOT NULL,
 `activity_description` text NOT NULL,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `date_start` date NOT NULL,
 `date_end` date NOT NULL,
 `total_cost` decimal(15,2) DEFAULT NULL,
 `location` varchar(255) DEFAULT NULL,
 `type` enum('documents','trainings','meetings','agreements','inputs','infrastructures','outputs') NOT NULL,
 `status` enum('pending','active','submitted','approved','rated') NOT NULL DEFAULT 'pending',
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `rating_score` decimal(3,2) DEFAULT NULL,
 `rated_at` datetime DEFAULT NULL,
 `rated_by` int(11) DEFAULT NULL,
 `rate_remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
activities_agreements	CREATE TABLE `activities_agreements` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `activity_id` int(11) NOT NULL,
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `agreement_type` varchar(100) DEFAULT NULL,
 `parties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`parties`)),
 `effective_date` date DEFAULT NULL,
 `expiry_date` date DEFAULT NULL,
 `status` enum('draft','active','expired','terminated','archived') DEFAULT 'draft',
 `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
 `remarks` text DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_effective_date` (`effective_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
activities_documents	CREATE TABLE `activities_documents` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `activity_id` int(11) DEFAULT NULL,
 `document_files` longtext DEFAULT NULL,
 `remarks` text NOT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
activities_infrastructure	CREATE TABLE `activities_infrastructure` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `activity_id` int(11) DEFAULT NULL,
 `infrastructure` varchar(255) NOT NULL,
 `gps_coordinates` varchar(100) DEFAULT NULL,
 `infrastructure_images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`infrastructure_images`)),
 `infrastructure_files` longtext DEFAULT NULL,
 `signing_scheet_filepath` varchar(500) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
activities_input	CREATE TABLE `activities_input` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `activity_id` int(11) DEFAULT NULL,
 `input_images` longtext DEFAULT NULL,
 `input_files` longtext DEFAULT NULL,
 `inputs` longtext DEFAULT NULL,
 `gps_coordinates` varchar(255) DEFAULT NULL,
 `signing_sheet_filepath` varchar(255) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
activities_meetings	CREATE TABLE `activities_meetings` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `branch_id` int(11) NOT NULL,
 `title` varchar(255) NOT NULL,
 `agenda` text DEFAULT NULL,
 `meeting_date` datetime NOT NULL,
 `start_time` datetime DEFAULT NULL,
 `end_time` datetime DEFAULT NULL,
 `location` varchar(255) DEFAULT NULL,
 `participants` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`participants`)),
 `status` enum('scheduled','in_progress','completed','cancelled') DEFAULT 'scheduled',
 `minutes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`minutes`)),
 `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
 `remarks` text DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_branch_id` (`branch_id`),
 KEY `idx_meeting_date` (`meeting_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
activities_output	CREATE TABLE `activities_output` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `activity_id` int(11) DEFAULT NULL,
 `outputs` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`outputs`)),
 `output_images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`output_images`)),
 `output_files` longtext DEFAULT NULL,
 `beneficiaries` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`beneficiaries`)),
 `total_value` decimal(15,2) DEFAULT NULL,
 `gps_coordinates` varchar(255) DEFAULT NULL,
 `signing_sheet_filepath` varchar(255) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
activities_training	CREATE TABLE `activities_training` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `activity_id` int(11) NOT NULL,
 `trainers` text DEFAULT NULL,
 `topics` text DEFAULT NULL,
 `trainees` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`trainees`)),
 `training_images` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`training_images`)),
 `training_files` longtext DEFAULT NULL,
 `gps_coordinates` varchar(255) DEFAULT NULL,
 `signing_sheet_filepath` varchar(255) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
adx_country	CREATE TABLE `adx_country` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `code` varchar(2) NOT NULL,
 `created_at` datetime NOT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
agreements	CREATE TABLE `agreements` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `branch_id` int(11) NOT NULL,
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `agreement_type` varchar(100) DEFAULT NULL,
 `parties` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`parties`)),
 `effective_date` date NOT NULL,
 `expiry_date` date DEFAULT NULL,
 `status` enum('draft','active','expired','terminated','archived') DEFAULT 'draft',
 `terms` text DEFAULT NULL,
 `conditions` text DEFAULT NULL,
 `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
 `remarks` text DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_branch_id` (`branch_id`),
 KEY `idx_effective_date` (`effective_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
audit_trail	CREATE TABLE `audit_trail` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `event` enum('create','read','update','delete') NOT NULL,
 `table_name` varchar(128) NOT NULL,
 `row_pk` varchar(64) NOT NULL,
 `changed_columns` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`changed_columns`)),
 `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
 `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
 `actor_id` bigint(20) DEFAULT NULL,
 `actor_type` varchar(32) DEFAULT NULL,
 `request_id` char(36) DEFAULT NULL,
 `ip_address` varbinary(16) DEFAULT NULL,
 `user_agent` varchar(255) DEFAULT NULL,
 `http_method` varchar(10) DEFAULT NULL,
 `url` text DEFAULT NULL,
 `notes` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 PRIMARY KEY (`id`),
 KEY `idx_audit_table_row` (`table_name`,`row_pk`),
 KEY `idx_audit_event` (`event`),
 KEY `idx_audit_actor` (`actor_id`),
 KEY `idx_audit_request` (`request_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
branches	CREATE TABLE `branches` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `parent_id` int(11) DEFAULT NULL,
 `abbrev` varchar(20) NOT NULL,
 `name` varchar(255) NOT NULL,
 `remarks` text NOT NULL,
 `branch_status` tinyint(4) NOT NULL DEFAULT 1,
 `branch_status_by` int(11) NOT NULL,
 `branch_status_at` datetime DEFAULT NULL,
 `branch_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_parent_id` (`parent_id`),
 KEY `idx_branch_status` (`branch_status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
commodities	CREATE TABLE `commodities` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `commodity_code` varchar(50) NOT NULL,
 `commodity_name` varchar(255) NOT NULL,
 `commodity_icon` text DEFAULT NULL,
 `commodity_color_code` varchar(10) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` varchar(100) DEFAULT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` varchar(100) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` varchar(100) DEFAULT NULL,
 `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
 PRIMARY KEY (`id`),
 UNIQUE KEY `commodity_code` (`commodity_code`),
 KEY `idx_commodity_name` (`commodity_name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
commodity_prices	CREATE TABLE `commodity_prices` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `commodity_id` int(11) unsigned NOT NULL,
 `price_date` date NOT NULL,
 `market_type` enum('local','export','wholesale','retail') DEFAULT 'local',
 `price_per_unit` decimal(15,2) NOT NULL,
 `unit_of_measurement` varchar(50) DEFAULT NULL,
 `currency` varchar(10) DEFAULT 'PGK',
 `location` varchar(255) DEFAULT NULL,
 `source` varchar(255) DEFAULT NULL,
 `notes` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` varchar(100) DEFAULT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` varchar(100) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` varchar(100) DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 PRIMARY KEY (`id`),
 KEY `idx_commodity_id` (`commodity_id`),
 KEY `idx_price_date` (`price_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci
commodity_production	CREATE TABLE `commodity_production` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `commodity_id` int(11) NOT NULL,
 `date_from` date NOT NULL,
 `date_to` date NOT NULL,
 `item` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `unit_of_measurement` varchar(50) DEFAULT NULL,
 `quantity` decimal(15,3) NOT NULL,
 `is_exported` tinyint(1) NOT NULL DEFAULT 0,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` varchar(100) DEFAULT NULL,
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` varchar(100) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` varchar(100) DEFAULT NULL,
 `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
 PRIMARY KEY (`id`),
 KEY `idx_commodity_id` (`commodity_id`),
 KEY `idx_date_from` (`date_from`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_users	CREATE TABLE `dakoii_users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `username` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `role` varchar(100) NOT NULL,
 `dakoii_user_status` tinyint(1) DEFAULT 1,
 `dakoii_user_status_remarks` text DEFAULT NULL,
 `dakoii_user_status_at` datetime DEFAULT NULL,
 `dakoii_user_status_by` int(11) DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `username` (`username`),
 KEY `idx_role` (`role`),
 KEY `idx_dakoii_user_status` (`dakoii_user_status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
documents	CREATE TABLE `documents` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `branch_id` int(11) NOT NULL,
 `folder_id` int(11) NOT NULL,
 `classification` enum('private','internal','public') NOT NULL,
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `doc_date` datetime DEFAULT NULL,
 `authors` text DEFAULT NULL,
 `file_path` varchar(520) NOT NULL,
 `file_type` varchar(100) NOT NULL,
 `file_size` varchar(100) NOT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_branch_id` (`branch_id`),
 KEY `idx_folder_id` (`folder_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
duty_instructions	CREATE TABLE `duty_instructions` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `workplan_id` bigint(20) unsigned NOT NULL,
 `user_id` int(11) NOT NULL,
 `supervisor_id` int(11) NOT NULL,
 `duty_instruction_number` varchar(50) NOT NULL,
 `duty_instruction_title` varchar(255) NOT NULL,
 `duty_instruction_description` text DEFAULT NULL,
 `duty_instruction_filepath` varchar(500) DEFAULT NULL,
 `status` varchar(50) DEFAULT 'pending',
 `status_by` bigint(20) unsigned DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` bigint(20) unsigned NOT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_workplan_id` (`workplan_id`),
 KEY `idx_status` (`status`),
 KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
duty_instructions_corporate_plan_link	CREATE TABLE `duty_instructions_corporate_plan_link` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `duty_items_id` bigint(20) unsigned NOT NULL,
 `corp_strategies_id` bigint(20) unsigned NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` bigint(20) unsigned NOT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_duty_items_id` (`duty_items_id`),
 KEY `idx_corp_strategies_id` (`corp_strategies_id`),
 KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
duty_instruction_items	CREATE TABLE `duty_instruction_items` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `duty_instruction_id` bigint(20) unsigned NOT NULL,
 `user_id` int(11) NOT NULL,
 `instruction_number` varchar(50) NOT NULL,
 `instruction` text NOT NULL,
 `status` varchar(50) DEFAULT 'active',
 `status_by` bigint(20) unsigned DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` bigint(20) unsigned NOT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_duty_instruction_id` (`duty_instruction_id`),
 KEY `idx_status` (`status`),
 KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
flyway_schema_history	CREATE TABLE `flyway_schema_history` (
 `installed_rank` int(11) NOT NULL,
 `version` varchar(50) DEFAULT NULL,
 `description` varchar(200) NOT NULL,
 `type` varchar(20) NOT NULL,
 `script` varchar(1000) NOT NULL,
 `checksum` int(11) DEFAULT NULL,
 `installed_by` varchar(100) NOT NULL,
 `installed_on` timestamp NOT NULL DEFAULT current_timestamp(),
 `execution_time` int(11) NOT NULL,
 `success` tinyint(1) NOT NULL,
 PRIMARY KEY (`installed_rank`),
 KEY `flyway_schema_history_s_idx` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
folders	CREATE TABLE `folders` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `branch_id` int(11) NOT NULL,
 `parent_folder_id` int(11) DEFAULT NULL,
 `name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `access` enum('private','internal','public') NOT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_branch_id` (`branch_id`),
 KEY `idx_parent_folder_id` (`parent_folder_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
gov_structure	CREATE TABLE `gov_structure` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `parent_id` int(11) DEFAULT NULL,
 `json_id` varchar(255) NOT NULL,
 `level` enum('province','district','llg','ward') NOT NULL COMMENT 'province, district, llg, ward',
 `code` varchar(20) NOT NULL,
 `name` varchar(255) NOT NULL,
 `flag_filepath` varchar(255) NOT NULL,
 `map_center` varchar(100) NOT NULL,
 `map_zoom` varchar(11) NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_parent_id` (`parent_id`),
 KEY `idx_level` (`level`),
 KEY `idx_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
meetings	CREATE TABLE `meetings` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `branch_id` int(11) NOT NULL,
 `title` varchar(255) NOT NULL,
 `agenda` text DEFAULT NULL,
 `meeting_date` datetime NOT NULL,
 `start_time` datetime DEFAULT NULL,
 `end_time` datetime DEFAULT NULL,
 `location` varchar(255) DEFAULT NULL,
 `participants` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`participants`)),
 `status` enum('scheduled','in_progress','completed','cancelled') DEFAULT 'scheduled',
 `minutes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`minutes`)),
 `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
 `recurrence_rule` varchar(255) DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `access_type` enum('private','internal','public') DEFAULT 'private',
 `is_deleted` tinyint(1) DEFAULT 0,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_branch_id` (`branch_id`),
 KEY `idx_meeting_date` (`meeting_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
migrations	CREATE TABLE `migrations` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `version` varchar(255) NOT NULL,
 `class` varchar(255) NOT NULL,
 `group` varchar(255) NOT NULL,
 `namespace` varchar(255) NOT NULL,
 `time` int(11) NOT NULL,
 `batch` int(11) unsigned NOT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci
org_settings	CREATE TABLE `org_settings` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `settings_code` varchar(100) NOT NULL,
 `settings_name` varchar(255) NOT NULL,
 `settings` text NOT NULL,
 `created_by` int(11) NOT NULL,
 `updated_by` int(11) NOT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `unique_settings_code` (`settings_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
performance_indicators_kra	CREATE TABLE `performance_indicators_kra` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `workplan_period_id` bigint(20) unsigned NOT NULL,
 `parent_id` bigint(20) unsigned DEFAULT NULL,
 `type` enum('kra','performance_indicator') NOT NULL,
 `code` varchar(100) DEFAULT NULL,
 `item` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `status_by` bigint(20) unsigned DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` bigint(20) unsigned NOT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_performance_period_id` (`workplan_period_id`),
 KEY `idx_parent_id` (`parent_id`),
 KEY `idx_type` (`type`),
 KEY `idx_code` (`code`),
 KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
performance_outputs	CREATE TABLE `performance_outputs` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `kra_performance_indicator_id` bigint(20) unsigned NOT NULL,
 `user_id` bigint(20) unsigned NOT NULL,
 `output` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `quantity` varchar(20) NOT NULL,
 `unit_of_measurement` varchar(255) NOT NULL,
 `status` varchar(50) DEFAULT 'pending',
 `status_by` bigint(20) unsigned DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` bigint(20) unsigned NOT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_kra_performance_indicator_id` (`kra_performance_indicator_id`),
 KEY `idx_user_id` (`user_id`),
 KEY `idx_status` (`status`),
 KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_corporate_plan	CREATE TABLE `plans_corporate_plan` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `parent_id` int(11) DEFAULT NULL,
 `type` varchar(100) NOT NULL,
 `code` varchar(20) NOT NULL,
 `title` text NOT NULL,
 `date_from` date DEFAULT NULL,
 `date_to` date DEFAULT NULL,
 `remarks` text NOT NULL,
 `corp_plan_status` tinyint(4) NOT NULL DEFAULT 1,
 `corp_plan_status_by` int(11) NOT NULL,
 `corp_plan_status_at` datetime DEFAULT NULL,
 `corp_plan_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp	CREATE TABLE `plans_mtdp` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `abbrev` varchar(20) NOT NULL,
 `title` varchar(255) NOT NULL,
 `date_from` date DEFAULT NULL,
 `date_to` date DEFAULT NULL,
 `remarks` text NOT NULL,
 `mtdp_status` tinyint(4) NOT NULL DEFAULT 1,
 `mtdp_status_by` int(11) NOT NULL,
 `mtdp_status_at` datetime DEFAULT NULL,
 `mtdp_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_abbrev` (`abbrev`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp_dip	CREATE TABLE `plans_mtdp_dip` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `mtdp_id` int(11) NOT NULL,
 `spa_id` int(11) NOT NULL,
 `dip_code` varchar(20) NOT NULL,
 `dip_title` varchar(255) NOT NULL,
 `dip_remarks` text NOT NULL,
 `investments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
 `kras` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
 `strategies` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
 `indicators` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
 `dip_status` tinyint(4) NOT NULL DEFAULT 1,
 `dip_status_by` int(11) NOT NULL,
 `dip_status_at` datetime DEFAULT NULL,
 `dip_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_mtdp_id` (`mtdp_id`),
 KEY `idx_spa_id` (`spa_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp_indicators	CREATE TABLE `plans_mtdp_indicators` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `mtdp_id` int(11) NOT NULL,
 `spa_id` int(11) NOT NULL,
 `dip_id` int(11) NOT NULL,
 `sa_id` int(11) NOT NULL,
 `investment_id` int(11) NOT NULL,
 `kra_id` int(11) NOT NULL,
 `strategies_id` int(11) NOT NULL,
 `indicator` text NOT NULL,
 `source` varchar(255) NOT NULL,
 `baseline` varchar(255) NOT NULL,
 `year_one` varchar(255) NOT NULL,
 `year_two` varchar(255) NOT NULL,
 `year_three` varchar(255) NOT NULL,
 `year_four` varchar(255) NOT NULL,
 `year_five` varchar(255) NOT NULL,
 `indicators_status` tinyint(4) NOT NULL DEFAULT 1,
 `indicators_status_by` int(11) NOT NULL,
 `indicators_status_at` datetime DEFAULT NULL,
 `indicators_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_mtdp_id` (`mtdp_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp_investments	CREATE TABLE `plans_mtdp_investments` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `mtdp_id` int(11) NOT NULL,
 `spa_id` int(11) NOT NULL,
 `sa_id` int(11) NOT NULL,
 `dip_id` int(11) NOT NULL,
 `dip_link_dip_id` int(11) NOT NULL,
 `investment` text NOT NULL,
 `year_one` decimal(18,2) NOT NULL DEFAULT 0.00,
 `year_two` decimal(18,2) NOT NULL DEFAULT 0.00,
 `year_three` decimal(18,2) NOT NULL DEFAULT 0.00,
 `year_four` decimal(18,2) NOT NULL DEFAULT 0.00,
 `year_five` decimal(18,2) NOT NULL DEFAULT 0.00,
 `funding_sources` text NOT NULL,
 `investment_status` tinyint(4) NOT NULL DEFAULT 1,
 `investment_status_by` int(11) NOT NULL,
 `investment_status_at` datetime DEFAULT NULL,
 `investment_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_mtdp_id` (`mtdp_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp_kra	CREATE TABLE `plans_mtdp_kra` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `mtdp_id` int(11) NOT NULL,
 `spa_id` int(11) NOT NULL,
 `dip_id` int(11) NOT NULL,
 `sa_id` int(11) NOT NULL,
 `investment_id` int(11) NOT NULL,
 `kpi` text NOT NULL,
 `year_one` varchar(255) NOT NULL,
 `year_two` varchar(255) NOT NULL,
 `year_three` varchar(255) NOT NULL,
 `year_four` varchar(255) NOT NULL,
 `year_five` varchar(255) NOT NULL,
 `responsible_agencies` varchar(255) NOT NULL,
 `kra_status` tinyint(4) NOT NULL DEFAULT 1,
 `kra_status_by` int(11) NOT NULL,
 `kra_status_at` datetime DEFAULT NULL,
 `kra_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_mtdp_id` (`mtdp_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp_spa	CREATE TABLE `plans_mtdp_spa` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `mtdp_id` int(11) NOT NULL,
 `code` varchar(20) NOT NULL,
 `title` varchar(255) NOT NULL,
 `remarks` text NOT NULL,
 `spa_status` tinyint(4) NOT NULL DEFAULT 1,
 `spa_status_by` int(11) NOT NULL,
 `spa_status_at` datetime DEFAULT NULL,
 `spa_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_mtdp_id` (`mtdp_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp_specific_area	CREATE TABLE `plans_mtdp_specific_area` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `mtdp_id` int(11) NOT NULL,
 `spa_id` int(11) NOT NULL,
 `dip_id` int(11) NOT NULL,
 `sa_code` varchar(20) NOT NULL,
 `sa_title` varchar(255) NOT NULL,
 `sa_remarks` text NOT NULL,
 `sa_status` tinyint(4) NOT NULL DEFAULT 1,
 `sa_status_by` int(11) NOT NULL,
 `sa_status_at` datetime DEFAULT NULL,
 `sa_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_mtdp_id` (`mtdp_id`),
 KEY `idx_spa_id` (`spa_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_mtdp_strategies	CREATE TABLE `plans_mtdp_strategies` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `mtdp_id` int(11) NOT NULL,
 `spa_id` int(11) NOT NULL,
 `dip_id` int(11) NOT NULL,
 `sa_id` int(11) NOT NULL,
 `investment_id` int(11) NOT NULL,
 `kra_id` int(11) NOT NULL,
 `strategy` text NOT NULL,
 `policy_reference` varchar(255) NOT NULL,
 `strategies_status` tinyint(4) NOT NULL DEFAULT 1,
 `strategies_status_by` int(11) NOT NULL,
 `strategies_status_at` datetime DEFAULT NULL,
 `strategies_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_mtdp_id` (`mtdp_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
plans_nasp	CREATE TABLE `plans_nasp` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `parent_id` int(11) DEFAULT NULL,
 `type` varchar(20) NOT NULL COMMENT 'type = plans, apas,  dips, specific_areas, objectives, outputs, indicators',
 `code` varchar(20) NOT NULL,
 `title` varchar(255) NOT NULL,
 `date_from` date DEFAULT NULL,
 `date_to` date DEFAULT NULL,
 `remarks` text NOT NULL,
 `nasp_status` tinyint(4) NOT NULL DEFAULT 1,
 `nasp_status_by` int(11) NOT NULL,
 `nasp_status_at` datetime DEFAULT NULL,
 `nasp_status_remarks` text NOT NULL,
 `created_at` datetime DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_parent_id` (`parent_id`),
 KEY `idx_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
regions	CREATE TABLE `regions` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `remarks` text DEFAULT NULL,
 `created_by` int(11) NOT NULL,
 `updated_by` int(11) NOT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
region_province_link	CREATE TABLE `region_province_link` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `region_id` int(11) unsigned NOT NULL,
 `province_id` int(11) unsigned NOT NULL,
 `created_by` int(11) NOT NULL,
 `updated_by` int(11) NOT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_region_id` (`region_id`),
 KEY `idx_province_id` (`province_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
sme	CREATE TABLE `sme` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `province_id` int(11) NOT NULL,
 `district_id` int(11) NOT NULL,
 `llg_id` int(11) NOT NULL,
 `village_name` varchar(255) DEFAULT NULL,
 `sme_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `gps_coordinates` varchar(100) DEFAULT NULL,
 `contact_details` text DEFAULT NULL,
 `logo_filepath` varchar(255) DEFAULT NULL,
 `status` varchar(50) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_by` int(11) DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_province_id` (`province_id`),
 KEY `idx_district_id` (`district_id`),
 KEY `idx_llg_id` (`llg_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
sme_staff	CREATE TABLE `sme_staff` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `sme_id` int(11) NOT NULL,
 `fname` varchar(100) NOT NULL,
 `lname` varchar(100) NOT NULL,
 `gender` varchar(11) NOT NULL,
 `dobirth` date DEFAULT NULL,
 `designation` varchar(100) DEFAULT NULL,
 `contacts` text DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `id_photo_path` varchar(255) DEFAULT NULL,
 `status` varchar(50) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_by` int(11) DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_sme_id` (`sme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
users	CREATE TABLE `users` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `ucode` varchar(200) NOT NULL,
 `password` varchar(255) DEFAULT NULL,
 `email` varchar(255) NOT NULL,
 `phone` text NOT NULL,
 `fname` varchar(255) NOT NULL,
 `lname` varchar(255) NOT NULL,
 `gender` enum('male','female') DEFAULT NULL,
 `dobirth` date DEFAULT NULL,
 `place_birth` varchar(255) DEFAULT NULL,
 `address` text DEFAULT NULL,
 `employee_number` varchar(100) DEFAULT NULL,
 `branch_id` int(11) DEFAULT NULL,
 `designation` varchar(255) DEFAULT NULL,
 `grade` varchar(100) DEFAULT NULL,
 `report_to_id` int(11) DEFAULT NULL,
 `is_evaluator` tinyint(1) NOT NULL DEFAULT 0,
 `is_supervisor` tinyint(1) NOT NULL DEFAULT 0,
 `is_admin` tinyint(3) NOT NULL DEFAULT 0,
 `commodity_id` int(11) DEFAULT NULL,
 `role` enum('user','guest') NOT NULL,
 `joined_date` date DEFAULT NULL,
 `id_photo_filepath` varchar(255) DEFAULT NULL,
 `user_status` tinyint(1) DEFAULT 1,
 `user_status_remarks` text DEFAULT NULL,
 `user_status_at` datetime DEFAULT NULL,
 `user_status_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 `activation_token` varchar(255) DEFAULT NULL COMMENT 'Secure token for account activation',
 `activation_expires_at` datetime DEFAULT NULL COMMENT 'Expiration timestamp for activation token',
 `activated_at` datetime DEFAULT NULL COMMENT 'Timestamp when user completed activation',
 `is_activated` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Activation status flag (0=pending, 1=activated)',
 PRIMARY KEY (`id`),
 UNIQUE KEY `ucode` (`ucode`),
 UNIQUE KEY `email` (`email`),
 KEY `idx_branch_id` (`branch_id`),
 KEY `idx_role` (`role`),
 KEY `idx_commodity_id` (`commodity_id`),
 KEY `idx_activation_token` (`activation_token`),
 KEY `idx_is_activated` (`is_activated`),
 KEY `idx_activation_expires` (`activation_expires_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
vulnerability	CREATE TABLE `vulnerability` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `province_id` int(11) DEFAULT NULL,
 `district_id` int(11) DEFAULT NULL,
 `vulnerability_type` varchar(255) DEFAULT NULL,
 `vulnerability_category` varchar(255) DEFAULT NULL,
 `description` text DEFAULT NULL,
 `severity_level` enum('low','medium','high','critical') DEFAULT 'medium',
 `affected_population` int(11) DEFAULT NULL,
 `geographic_scope` varchar(255) DEFAULT NULL,
 `seasonal_pattern` varchar(255) DEFAULT NULL,
 `risk_factors` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`risk_factors`)),
 `impact_assessment` text DEFAULT NULL,
 `coping_mechanisms` text DEFAULT NULL,
 `intervention_needs` text DEFAULT NULL,
 `priority_ranking` int(11) DEFAULT NULL,
 `data_source` varchar(255) DEFAULT NULL,
 `assessment_date` date DEFAULT NULL,
 `next_review_date` date DEFAULT NULL,
 `vulnerability_status` tinyint(1) DEFAULT 1,
 `vulnerability_status_by` int(11) DEFAULT NULL,
 `vulnerability_status_at` datetime DEFAULT NULL,
 `vulnerability_status_remarks` text DEFAULT NULL,
 `created_by` int(11) DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_province_id` (`province_id`),
 KEY `idx_district_id` (`district_id`),
 KEY `idx_severity_level` (`severity_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplans	CREATE TABLE `workplans` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `branch_id` int(11) NOT NULL,
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `supervisor_id` int(11) DEFAULT NULL,
 `start_date` date NOT NULL,
 `end_date` date DEFAULT NULL,
 `status` enum('draft','in_progress','completed','on_hold','cancelled') DEFAULT 'draft',
 `objectives` longtext DEFAULT NULL,
 `remarks` text DEFAULT NULL,
 `is_deleted` tinyint(1) DEFAULT 0,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_branch_id` (`branch_id`),
 KEY `idx_supervisor_id` (`supervisor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplan_activities	CREATE TABLE `workplan_activities` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `workplan_id` int(11) NOT NULL,
 `branch_id` int(11) DEFAULT NULL,
 `activity_code` varchar(20) NOT NULL,
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `activity_type` enum('training','inputs','infrastructure','output') NOT NULL,
 `q_one_target` decimal(15,2) DEFAULT NULL,
 `q_two_target` decimal(15,2) DEFAULT NULL,
 `q_three_target` decimal(15,2) DEFAULT NULL,
 `q_four_target` decimal(15,2) DEFAULT NULL,
 `q_one_achieved` decimal(15,2) DEFAULT NULL,
 `q_two_achieved` decimal(15,2) DEFAULT NULL,
 `q_three_achieved` decimal(15,2) DEFAULT NULL,
 `q_four_achieved` decimal(15,2) DEFAULT NULL,
 `total_budget` decimal(15,2) DEFAULT NULL,
 `rated_at` datetime DEFAULT NULL,
 `rated_by` int(11) DEFAULT NULL,
 `rating` int(11) DEFAULT NULL,
 `reated_remarks` text DEFAULT NULL,
 `supervisor_id` int(11) DEFAULT NULL,
 `status` varchar(50) DEFAULT NULL,
 `status_by` int(11) DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_workplan_id` (`workplan_id`),
 KEY `idx_activity_type` (`activity_type`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplan_corporate_plan_link	CREATE TABLE `workplan_corporate_plan_link` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `workplan_id` int(11) DEFAULT NULL,
 `workplan_activity_id` int(11) NOT NULL,
 `corporate_plan_id` int(11) NOT NULL,
 `link_type` varchar(255) DEFAULT NULL,
 `alignment_notes` text DEFAULT NULL,
 `overarching_objective_id` int(11) DEFAULT NULL,
 `objective_id` int(11) DEFAULT NULL,
 `kra_id` int(11) DEFAULT NULL,
 `strategies_id` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_workplan_activity_id` (`workplan_activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplan_mtdp_link	CREATE TABLE `workplan_mtdp_link` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `workplan_id` int(11) DEFAULT NULL,
 `workplan_activity_id` int(11) NOT NULL,
 `mtdp_id` int(11) DEFAULT NULL,
 `spa_id` int(11) DEFAULT NULL,
 `dip_id` int(11) DEFAULT NULL,
 `sa_id` int(11) DEFAULT NULL,
 `investment_id` int(11) DEFAULT NULL,
 `kra_id` int(11) DEFAULT NULL,
 `strategies_id` int(11) DEFAULT NULL,
 `indicators_id` int(11) DEFAULT NULL,
 `link_type` varchar(255) DEFAULT NULL,
 `alignment_notes` text DEFAULT NULL,
 `indicator_id` int(11) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_workplan_activity_id` (`workplan_activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplan_nasp_link	CREATE TABLE `workplan_nasp_link` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `workplan_id` int(11) DEFAULT NULL,
 `workplan_activity_id` int(11) NOT NULL,
 `nasp_id` int(11) NOT NULL,
 `apa_id` int(11) DEFAULT NULL,
 `dip_id` int(11) DEFAULT NULL,
 `specific_area_id` int(11) DEFAULT NULL,
 `objective_id` int(11) DEFAULT NULL,
 `output_id` int(11) DEFAULT NULL,
 `link_type` varchar(255) DEFAULT NULL,
 `alignment_notes` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_workplan_activity_id` (`workplan_activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplan_others_link	CREATE TABLE `workplan_others_link` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `workplan_id` int(11) DEFAULT NULL,
 `external_plan_name` varchar(255) DEFAULT NULL,
 `external_plan_type` varchar(255) DEFAULT NULL,
 `link_description` text DEFAULT NULL,
 `alignment_notes` text DEFAULT NULL,
 `workplan_activity_id` int(11) NOT NULL,
 `link_type` enum('recurrent','special_project','emergency','other') NOT NULL DEFAULT 'other',
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `justification` text NOT NULL,
 `category` varchar(100) DEFAULT NULL,
 `priority_level` enum('low','medium','high','critical') DEFAULT 'medium',
 `expected_outcome` text DEFAULT NULL,
 `target_beneficiaries` text DEFAULT NULL,
 `budget_estimate` decimal(15,2) DEFAULT NULL,
 `duration_months` int(11) DEFAULT NULL,
 `start_date` date DEFAULT NULL,
 `end_date` date DEFAULT NULL,
 `status` enum('active','inactive','completed','cancelled') DEFAULT 'active',
 `remarks` text DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `created_by` int(11) DEFAULT NULL,
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `updated_by` int(11) DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_workplan_activity_id` (`workplan_activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
workplan_period	CREATE TABLE `workplan_period` (
 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
 `user_id` bigint(20) unsigned NOT NULL,
 `duty_instruction_id` bigint(20) unsigned DEFAULT NULL,
 `title` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `workplan_period_filepath` varchar(500) DEFAULT NULL,
 `status` varchar(50) DEFAULT 'pending',
 `status_by` bigint(20) unsigned DEFAULT NULL,
 `status_at` datetime DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` bigint(20) unsigned NOT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` bigint(20) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` bigint(20) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_user_id` (`user_id`),
 KEY `idx_duty_instruction_id` (`duty_instruction_id`),
 KEY `idx_status` (`status`),
 KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci